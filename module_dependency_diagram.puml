@startuml CRM FMCG TPM 系统调用关系图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam packageStyle rectangle
skinparam componentStyle rectangle
skinparam arrowThickness 2
skinparam roundcorner 10

title CRM FMCG TPM 系统完整调用关系图

note top
  <b>系统架构说明：</b>
  • 基于Spring Boot + Maven多模块架构
  • 使用MongoDB作为主要数据存储
  • 通过Dubbo进行微服务间通信
  • 采用分层架构：Controller -> Service -> Manager -> DAO -> PO
  • 支持多租户和灰度发布
end note

package "fs-crm-fmcg-service" as ServiceModule {
    package "web.facade" {
        [RewardController]
        [POCActivityController]
        [WithdrawalController]
        [MengNiuRewardController]
    }

    package "web.inner" {
        [InnerDMSController]
        [InnerRewardController]
        [InnerYLTPMController]
    }

    package "web.task" {
        [POCTaskController]
    }

    [CRMInitService]
}

package "fs-crm-fmcg-tpm" as TPMModule {
    package "web.service" {
        [BudgetNewConsumeRuleService]
        [BudgetClosureService]
        [LicenseService]
        [PhysicalRewardService]
        [ScanCodeService]
        [UnlockOuterCodeService]
        [WechatService]
    }

    package "web.manager" {
        [ActivityTypeManager]
        [BudgetAccrualRuleManager]
        [ProofPeriodManager]
    }

    package "business" {
        [ActivityService]
        [AsyncBudgetDisassemblyService]
        [BudgetService]
        [StoreBusiness]
        [TPMDisplayReportService]
        [TPMTriggerActionService]
    }

    package "dao.mongo" {
        [BaseDAO]
        [UniqueIdBaseDAO]
        [ActivityTypeDAO]
        [BudgetNewConsumeRuleDAO]
        [POCRecordDAO]
        [ActivityRewardRuleDAO]
    }

    package "dao.mongo.po" {
        [MongoPO]
        [ActivityTypePO]
        [BudgetNewConsumeRulePO]
        [POCRecordPO]
        [ConfigPO]
    }

    package "service" {
        [OrganizationServiceImpl]
        [ScriptServiceImpl]
        [TPMRoleService]
        [TransactionProxy]
    }
}

package "fs-crm-fmcg-mengniu" as MengNiuModule {
    [NewRewardService]
    [RedPacketEventDistributor]
    [ConsumerRewardHandler]
    package "dao" {
        [MengNiuBaseDAO]
    }
}

package "fs-crm-fmcg-ocr" as OCRModule {
    [WinePriceService]
    [OcrAdapter]
    [Analyzer]
}

package "fs-crm-fmcg-fesco" as FescoModule {
    [FescoService]
}

package "fs-crm-fmcg-common" as CommonModule {
    package "http" {
        [ApiContextManager]
        [ApiContext]
    }

    package "adapter" {
        [ReceiveMoneyService]
        [IFMCGTokenService]
    }

    package "utils" {
        [EncryptionService]
        [QueryDataUtil]
        [SearchQueryUtil]
    }

    package "gray" {
        [TPMGrayUtils]
    }
}

package "外部框架依赖" as ExternalFramework {
    [Spring Framework]
    [MongoDB]
    [Redis]
    [PaaS平台]
    [微信API]
    [支付宝API]
}

' 模块间依赖关系
ServiceModule --> TPMModule : 依赖
ServiceModule --> MengNiuModule : 依赖
ServiceModule --> CommonModule : 依赖

TPMModule --> CommonModule : 依赖
TPMModule --> ExternalFramework : 依赖

MengNiuModule --> CommonModule : 依赖
MengNiuModule --> TPMModule : 依赖

OCRModule --> CommonModule : 依赖

FescoModule --> CommonModule : 依赖

' 内部依赖关系
[RewardController] --> [BudgetNewConsumeRuleService]
[RewardController] --> [ScanCodeService]
[RewardController] --> [PhysicalRewardService]

[POCActivityController] --> [ActivityService]

[BudgetNewConsumeRuleService] --> [BudgetAccrualRuleManager]
[BudgetNewConsumeRuleService] --> [AsyncBudgetDisassemblyService]

[ActivityTypeManager] --> [ActivityService]
[ActivityTypeManager] --> [TPMTriggerActionService]

[ActivityService] --> [ActivityTypeDAO]
[BudgetService] --> [BudgetNewConsumeRuleDAO]

[ActivityTypeDAO] --> [ActivityTypePO]
[BudgetNewConsumeRuleDAO] --> [BudgetNewConsumeRulePO]

[BaseDAO] --> [MongoPO]
[UniqueIdBaseDAO] --> [MongoPO]

' 外部依赖
[ScanCodeService] --> [微信API]
[PhysicalRewardService] --> [支付宝API]
[BaseDAO] --> [MongoDB]
[BudgetService] --> [Redis]

note top of ServiceModule
  Web层模块
  提供REST API接口
  处理HTTP请求响应
end note

note top of TPMModule
  核心业务模块
  包含TPM相关的所有业务逻辑
  数据访问和业务处理
end note

note top of MengNiuModule
  蒙牛专用模块
  处理蒙牛特定的业务逻辑
  奖励和红包相关功能
end note

note top of CommonModule
  公共模块
  提供通用工具和服务
  被其他模块共同依赖
end note

note bottom of ExternalFramework
  外部框架和服务
  提供基础设施支持
end note

@enduml
