@startuml 系统架构分层图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam packageStyle rectangle
skinparam componentStyle rectangle
skinparam arrowThickness 2

title CRM FMCG TPM 系统架构分层图

note top
  <b>系统架构说明：</b>
  • 展示系统的完整分层架构和数据流向
  • 体现各层的职责分工和技术选型
  • 突出关键的架构模式和设计原则
  • 说明系统的扩展性和可维护性设计
end note

package "客户端层 (Client Layer)" as ClientLayer {
    component [Web前端] <<Frontend>> {
        • React/Vue.js单页应用
        • 响应式设计支持移动端
        • 实时数据更新和状态管理
        • 用户交互和数据展示
    }
    component [移动端App] <<Mobile>> {
        • 原生iOS/Android应用
        • 扫码功能和相机集成
        • 推送通知和离线缓存
        • 用户身份验证和授权
    }
    component [微信小程序] <<MiniProgram>> {
        • 微信生态集成
        • 扫码奖励和红包发放
        • 用户信息获取和验证
        • 社交分享和传播
    }
}

package "网关层 (Gateway Layer)" as GatewayLayer {
    component [API网关] <<Gateway>> {
        • 统一入口和路由分发
        • 请求限流和熔断保护
        • 身份认证和权限验证
        • 日志记录和监控统计
    }
    component [负载均衡器] <<LoadBalancer>> {
        • 流量分发和负载均衡
        • 健康检查和故障转移
        • SSL终结和安全防护
        • 静态资源缓存和CDN
    }
}

package "应用服务层 (Application Layer)" as ApplicationLayer {
    package "Web控制器层" {
        component [对外接口控制器] <<Controller>> {
            • RewardController - 奖励管理
            • POCActivityController - POC活动
            • WithdrawalController - 提现管理
            • MengNiuRewardController - 蒙牛奖励
            • AppComponentController - 应用组件
        }
        component [内部接口控制器] <<Controller>> {
            • InnerDMSController - DMS内部接口
            • InnerRewardController - 奖励内部接口
            • ModuleInitializationController - 模块初始化
            • CommonMongoController - 通用数据接口
        }
        component [任务控制器] <<Controller>> {
            • POCTaskController - 定时任务
            • 批处理任务调度
            • 异步任务监控
            • 任务结果通知
        }
    }
}

package "业务服务层 (Business Service Layer)" as BusinessLayer {
    package "核心业务服务" {
        component [TPM业务服务] <<Service>> {
            • BudgetNewConsumeRuleService - 预算消费规则
            • BudgetClosureService - 预算关闭
            • PhysicalRewardService - 实物奖励
            • ScanCodeService - 扫码服务
            • WechatService - 微信服务
        }
        component [专业业务服务] <<Service>> {
            • MengNiuRewardService - 蒙牛奖励
            • IntegralService - 积分服务
            • DMSService - 分销管理
            • OCRService - 图像识别
        }
    }
    
    package "业务管理层" {
        component [业务管理器] <<Manager>> {
            • ActivityTypeManager - 活动类型管理
            • BudgetAccrualRuleManager - 预算积累规则
            • ProofPeriodManager - 凭证期间管理
            • RewardRuleManager - 奖励规则管理
        }
    }
}

package "核心业务层 (Core Business Layer)" as CoreLayer {
    component [活动业务] <<Business>> {
        • ActivityService - 活动核心业务
        • 活动生命周期管理
        • 活动状态流转控制
        • 活动规则引擎执行
    }
    component [预算业务] <<Business>> {
        • BudgetService - 预算核心业务
        • AsyncBudgetDisassemblyService - 异步预算拆解
        • 预算计算和分配算法
        • 分布式锁和并发控制
    }
    component [门店业务] <<Business>> {
        • StoreBusiness - 门店业务逻辑
        • 门店权限和授权管理
        • 门店数据同步和更新
        • 门店级别的业务规则
    }
    component [报告业务] <<Business>> {
        • TPMDisplayReportService - 陈列报告
        • TPMTriggerActionService - 触发动作
        • 数据分析和统计计算
        • 报表生成和导出功能
    }
}

package "数据访问层 (Data Access Layer)" as DataLayer {
    package "DAO层" {
        component [基础DAO] <<DAO>> {
            • BaseDAO - 基础数据访问
            • UniqueIdBaseDAO - 唯一ID访问
            • 通用CRUD操作封装
            • 多租户数据隔离
        }
        component [业务DAO] <<DAO>> {
            • ActivityTypeDAO - 活动类型数据
            • BudgetNewConsumeRuleDAO - 预算规则数据
            • POCRecordDAO - POC记录数据
            • ActivityRewardRuleDAO - 奖励规则数据
        }
        component [配置DAO] <<DAO>> {
            • ConfigDAO - 系统配置数据
            • BizCodeDAO - 业务编码数据
            • 配置版本管理
            • 动态配置更新
        }
    }
}

package "数据模型层 (Data Model Layer)" as ModelLayer {
    component [实体模型] <<Entity>> {
        • MongoPO - 基础实体
        • ActivityTypePO - 活动类型实体
        • BudgetNewConsumeRulePO - 预算规则实体
        • POCRecordPO - POC记录实体
        • ConfigPO - 配置实体
    }
    component [数据传输对象] <<DTO>> {
        • 请求参数对象 (Arg)
        • 响应结果对象 (Result)
        • 数据传输对象 (DTO)
        • 视图对象 (VO)
    }
}

package "基础设施层 (Infrastructure Layer)" as InfraLayer {
    package "数据存储" {
        component [MongoDB集群] <<Database>> {
            • 主从复制和分片
            • 文档型数据存储
            • 索引优化和查询性能
            • 数据备份和恢复
        }
        component [Redis集群] <<Cache>> {
            • 分布式缓存
            • 会话存储
            • 分布式锁
            • 消息队列
        }
        component [Elasticsearch] <<Search>> {
            • 全文搜索引擎
            • 日志分析和聚合
            • 实时数据索引
            • 复杂查询和统计
        }
    }
    
    package "中间件服务" {
        component [Dubbo服务] <<RPC>> {
            • 分布式服务调用
            • 服务注册和发现
            • 负载均衡和容错
            • 服务监控和治理
        }
        component [消息队列] <<MQ>> {
            • 异步消息处理
            • 事件驱动架构
            • 削峰填谷
            • 系统解耦
        }
        component [定时任务] <<Scheduler>> {
            • XXL-Job分布式调度
            • 任务执行和监控
            • 失败重试和告警
            • 任务依赖管理
        }
    }
}

package "外部服务层 (External Service Layer)" as ExternalLayer {
    component [PaaS平台] <<Platform>> {
        • 元数据管理服务
        • 多租户管理
        • 权限和角色管理
        • 工作流引擎
    }
    component [微信开放平台] <<External>> {
        • 用户身份验证
        • 微信支付接口
        • 红包发放API
        • 小程序服务
    }
    component [支付宝开放平台] <<External>> {
        • 支付和转账
        • 实名认证
        • 风控服务
        • 小程序集成
    }
    component [第三方服务] <<External>> {
        • OCR识别服务
        • 短信通知服务
        • 邮件发送服务
        • 文件存储服务
    }
}

package "公共组件层 (Common Component Layer)" as CommonLayer {
    component [工具组件] <<Util>> {
        • QueryDataUtil - 查询工具
        • SearchQueryUtil - 搜索工具
        • EncryptionService - 加密服务
        • TPMGrayUtils - 灰度工具
    }
    component [适配器组件] <<Adapter>> {
        • ReceiveMoneyService - 收款适配器
        • IFMCGTokenService - 令牌服务
        • OcrAdapter - OCR适配器
        • 第三方服务适配器
    }
    component [上下文管理] <<Context>> {
        • ApiContextManager - 上下文管理
        • ApiContext - 请求上下文
        • 线程本地存储
        • 上下文传递和继承
    }
}

package "监控运维层 (Monitoring Layer)" as MonitoringLayer {
    component [应用监控] <<Monitor>> {
        • 应用性能监控 (APM)
        • 业务指标监控
        • 异常告警和通知
        • 链路追踪和分析
    }
    component [日志管理] <<Log>> {
        • 集中式日志收集
        • 日志分析和检索
        • 操作审计日志
        • 安全事件日志
    }
    component [运维工具] <<DevOps>> {
        • 自动化部署
        • 配置管理
        • 容器编排
        • 蓝绿部署
    }
}

' ========== 数据流向 ==========
ClientLayer --> GatewayLayer : HTTP/HTTPS请求
GatewayLayer --> ApplicationLayer : 路由分发
ApplicationLayer --> BusinessLayer : 业务调用
BusinessLayer --> CoreLayer : 核心业务
CoreLayer --> DataLayer : 数据访问
DataLayer --> ModelLayer : 对象映射
DataLayer --> InfraLayer : 数据存储

' ========== 横向依赖 ==========
ApplicationLayer --> CommonLayer : 公共组件
BusinessLayer --> CommonLayer : 工具服务
CoreLayer --> CommonLayer : 上下文管理
DataLayer --> CommonLayer : 查询工具

' ========== 外部集成 ==========
BusinessLayer --> ExternalLayer : 外部服务调用
InfraLayer --> ExternalLayer : 平台服务

' ========== 监控集成 ==========
ApplicationLayer --> MonitoringLayer : 监控数据
BusinessLayer --> MonitoringLayer : 业务指标
InfraLayer --> MonitoringLayer : 系统指标

note bottom
  <b>架构设计原则：</b>
  1. <b>分层解耦</b>：清晰的分层架构，上层依赖下层，同层之间通过接口交互
  2. <b>职责单一</b>：每层都有明确的职责，避免跨层直接调用
  3. <b>可扩展性</b>：支持水平扩展和垂直扩展，模块化设计便于功能扩展
  4. <b>高可用性</b>：通过集群部署、负载均衡、故障转移保证系统可用性
  5. <b>数据一致性</b>：通过事务管理、分布式锁、最终一致性保证数据正确性
  6. <b>安全性</b>：多层次的安全防护，包括网关认证、服务鉴权、数据加密
end note

note bottom
  <b>技术栈总结：</b>
  • <b>前端技术</b>：React/Vue.js + 移动端原生 + 微信小程序
  • <b>后端框架</b>：Spring Boot + Spring Cloud + Dubbo
  • <b>数据存储</b>：MongoDB + Redis + Elasticsearch
  • <b>中间件</b>：消息队列 + 定时任务 + 分布式锁
  • <b>监控运维</b>：APM + 日志分析 + 自动化部署
  • <b>外部集成</b>：微信/支付宝开放平台 + PaaS平台 + 第三方服务
end note

@enduml
