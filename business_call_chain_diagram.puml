@startuml 典型业务流程调用链图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60

title CRM FMCG TPM 系统典型业务流程调用链

note top
  <b>业务流程说明：</b>
  • 展示三个核心业务流程的完整调用链
  • 包含奖励发放、预算消费、活动管理的典型场景
  • 体现系统的分层架构和组件间协作
  • 突出事务管理、缓存使用、外部服务集成等关键技术点
end note

== 流程1：消费者扫码奖励发放 ==

actor "消费者" as Consumer
participant "RewardController" as RewardCtrl
participant "ScanCodeService" as ScanService
participant "WechatService" as WechatSvc
participant "PhysicalRewardService" as RewardSvc
participant "ActivityService" as ActivityBiz
participant "BudgetService" as BudgetBiz
participant "ActivityTypeDAO" as ActivityDAO
participant "BudgetNewConsumeRuleDAO" as BudgetDAO
participant "TransactionProxy" as TxProxy
participant "MongoDB" as MongoDB
participant "Redis" as Redis
participant "微信API" as WechatAPI

note over Consumer
  消费者使用微信扫描
  产品包装上的二维码
  期望获得奖励
end note

Consumer -> RewardCtrl: POST /reward/scan
note right: 扫码奖励请求\n包含二维码内容和用户信息

activate RewardCtrl
RewardCtrl -> ScanService: consumerScanCode(arg)
note right: 调用扫码服务\n验证二维码有效性

activate ScanService
ScanService -> WechatSvc: validateUser(userId)
note right: 验证微信用户身份\n确保用户合法性

activate WechatSvc
WechatSvc -> WechatAPI: getUserInfo(openId)
note right: 调用微信API\n获取用户详细信息
WechatAPI --> WechatSvc: userInfo
WechatSvc --> ScanService: validation result
deactivate WechatSvc

ScanService -> ActivityBiz: findActiveActivity(code)
note right: 查找二维码对应的\n有效活动信息

activate ActivityBiz
ActivityBiz -> ActivityDAO: queryByCode(tenantId, code)
note right: 查询活动数据\n包含活动规则和状态
activate ActivityDAO
ActivityDAO -> MongoDB: find(query)
MongoDB --> ActivityDAO: activity data
deactivate ActivityDAO
ActivityBiz --> ScanService: activity info
deactivate ActivityBiz

ScanService -> BudgetBiz: checkBudgetAvailable(activityId)
note right: 检查活动预算\n确保有足够余额发放奖励

activate BudgetBiz
BudgetBiz -> Redis: getBudgetLock(activityId)
note right: 获取分布式锁\n防止并发修改预算
Redis --> BudgetBiz: lock acquired

BudgetBiz -> BudgetDAO: getBudgetInfo(activityId)
note right: 查询预算详细信息\n包含余额和消费规则
activate BudgetDAO
BudgetDAO -> MongoDB: find(budgetQuery)
MongoDB --> BudgetDAO: budget data
deactivate BudgetDAO
BudgetBiz --> ScanService: budget status
deactivate BudgetBiz

ScanService -> RewardSvc: calculateReward(activity, user)
note right: 计算奖励金额\n根据用户等级和活动规则

activate RewardSvc
RewardSvc -> ActivityBiz: getRewardRule(activityId)
note right: 获取奖励规则\n包含奖励类型和计算方式
activate ActivityBiz
ActivityBiz -> ActivityDAO: getRewardRules(activityId)
activate ActivityDAO
ActivityDAO -> MongoDB: find(ruleQuery)
MongoDB --> ActivityDAO: reward rules
deactivate ActivityDAO
ActivityBiz --> RewardSvc: reward rules
deactivate ActivityBiz

RewardSvc -> TxProxy: beginTransaction()
note right: 开始事务\n确保奖励发放的原子性
activate TxProxy

RewardSvc -> BudgetBiz: consumeBudget(activityId, amount)
note right: 消费活动预算\n扣减相应金额
activate BudgetBiz
BudgetBiz -> BudgetDAO: updateBudget(activityId, -amount)
activate BudgetDAO
BudgetDAO -> MongoDB: update(budgetUpdate)
MongoDB --> BudgetDAO: update result
deactivate BudgetDAO
BudgetBiz --> RewardSvc: consume result
deactivate BudgetBiz

RewardSvc -> WechatSvc: sendReward(userId, amount)
note right: 发放微信红包\n调用微信支付API
activate WechatSvc
WechatSvc -> WechatAPI: sendRedPacket(params)
WechatAPI --> WechatSvc: payment result
WechatSvc --> RewardSvc: reward result
deactivate WechatSvc

RewardSvc -> TxProxy: commitTransaction()
note right: 提交事务\n确保所有操作成功
TxProxy --> RewardSvc: commit success
deactivate TxProxy

RewardSvc --> ScanService: reward success
deactivate RewardSvc
ScanService --> RewardCtrl: scan result
deactivate ScanService
RewardCtrl --> Consumer: 奖励发放成功
deactivate RewardCtrl

== 流程2：预算消费审批 ==

actor "业务人员" as User
participant "BudgetController" as BudgetCtrl
participant "BudgetNewConsumeRuleService" as BudgetService
participant "BudgetAccrualRuleManager" as BudgetMgr
participant "AsyncBudgetDisassemblyService" as AsyncBudget
participant "BudgetService" as BudgetBiz
participant "BudgetNewConsumeRuleDAO" as BudgetDAO
participant "TransactionProxy" as TxProxy
participant "MongoDB" as MongoDB
participant "Redis" as Redis

note over User
  业务人员提交预算消费申请
  需要经过规则验证和审批流程
end note

User -> BudgetCtrl: POST /budget/consume
note right: 提交预算消费请求\n包含预算ID和消费金额

activate BudgetCtrl
BudgetCtrl -> BudgetService: consumeBudget(arg)
note right: 调用预算消费服务\n处理预算扣减逻辑

activate BudgetService
BudgetService -> BudgetMgr: validateBudgetConsume(arg)
note right: 校验预算消费请求\n检查参数和权限

activate BudgetMgr
BudgetMgr -> BudgetBiz: checkBudgetAvailable(budgetId)
note right: 检查预算可用性\n确保余额充足

activate BudgetBiz
BudgetBiz -> BudgetDAO: getBudgetById(budgetId)
note right: 查询预算详细信息\n包括余额和状态
activate BudgetDAO
BudgetDAO -> MongoDB: query budget
note right: 执行预算查询\n获取最新数据
MongoDB --> BudgetDAO: budget data
deactivate BudgetDAO
BudgetBiz --> BudgetMgr: budget status
note left: 返回预算状态\n包含可用余额
deactivate BudgetBiz
BudgetMgr --> BudgetService: validation result
note left: 返回校验结果\n确认可以执行消费
deactivate BudgetMgr

BudgetService -> BudgetBiz: lockBudget(budgetId)
note right: 获取预算分布式锁\n防止并发修改冲突
activate BudgetBiz
BudgetBiz -> Redis: acquireLock(budgetId)
note right: 使用Redis分布式锁\n设置超时时间防止死锁
Redis --> BudgetBiz: lock acquired
BudgetBiz --> BudgetService: lock acquired
note left: 锁获取成功\n可以安全修改预算
deactivate BudgetBiz

BudgetService -> TxProxy: beginTransaction()
note right: 开始分布式事务\n确保数据一致性
activate TxProxy

BudgetService -> BudgetDAO: updateBudgetAmount(budgetId, amount)
note right: 更新预算余额\n执行原子性操作
activate BudgetDAO
BudgetDAO -> MongoDB: update budget
note right: 执行数据库更新\n使用事务确保一致性
MongoDB --> BudgetDAO: update result
deactivate BudgetDAO

BudgetService -> BudgetDAO: saveBudgetDetail(detail)
note right: 保存预算消费明细\n记录消费轨迹
activate BudgetDAO
BudgetDAO -> MongoDB: insert budget detail
note right: 插入消费记录\n包含时间和操作人
MongoDB --> BudgetDAO: detail id
deactivate BudgetDAO

BudgetService -> AsyncBudget: triggerBudgetDisassembly(budgetId)
note right: 触发异步预算拆解\n处理复杂的预算分配逻辑
activate AsyncBudget
AsyncBudget -> Redis: addToQueue(disassemblyTask)
note right: 添加到异步处理队列\n后台处理预算拆解
Redis --> AsyncBudget: task queued
AsyncBudget --> BudgetService: disassembly triggered
deactivate AsyncBudget

BudgetService -> TxProxy: commitTransaction()
note right: 提交事务\n确保所有操作成功
TxProxy --> BudgetService: commit success
deactivate TxProxy

BudgetService -> BudgetBiz: unlockBudget(budgetId)
note right: 释放预算分布式锁\n允许其他操作继续
activate BudgetBiz
BudgetBiz -> Redis: releaseLock(budgetId)
Redis --> BudgetBiz: lock released
BudgetBiz --> BudgetService: lock released
note left: 锁释放成功\n事务完成
deactivate BudgetBiz

BudgetService --> BudgetCtrl: ConsumeBudget.Result
note left: 返回消费结果\n包含新余额和记录ID
deactivate BudgetService
BudgetCtrl --> User: consume result
note left: 返回消费成功信息\n显示操作结果
deactivate BudgetCtrl

== 流程3：活动类型初始化 ==

actor "管理员" as Admin
participant "ModuleInitializationController" as InitCtrl
participant "ActivityTypeManager" as ActivityMgr
participant "ActivityService" as ActivityBiz
participant "TPMTriggerActionService" as TriggerSvc
participant "ActivityTypeDAO" as ActivityDAO
participant "ConfigDAO" as ConfigDAO
participant "TransactionProxy" as TxProxy
participant "MongoDB" as MongoDB
participant "PaaS平台" as PaaS

note over Admin
  系统管理员初始化新的活动类型
  包含元数据创建和业务规则配置
end note

Admin -> InitCtrl: POST /init/activityType
note right: 初始化活动类型请求\n包含活动类型配置信息

activate InitCtrl
InitCtrl -> ActivityMgr: initActivityType(config)
note right: 调用活动类型管理器\n处理初始化逻辑

activate ActivityMgr
ActivityMgr -> PaaS: createObjectMetadata(typeConfig)
note right: 在PaaS平台创建对象元数据\n定义活动类型的字段和属性
activate PaaS
PaaS --> ActivityMgr: metadata created
deactivate PaaS

ActivityMgr -> TxProxy: beginTransaction()
note right: 开始事务\n确保初始化操作的原子性
activate TxProxy

ActivityMgr -> ActivityDAO: createActivityType(typeData)
note right: 创建活动类型数据\n保存到MongoDB
activate ActivityDAO
ActivityDAO -> MongoDB: insert activity type
MongoDB --> ActivityDAO: type id
deactivate ActivityDAO

ActivityMgr -> ConfigDAO: saveDefaultConfig(typeId, config)
note right: 保存默认配置\n包含活动类型的默认参数
activate ConfigDAO
ConfigDAO -> MongoDB: insert config
MongoDB --> ConfigDAO: config id
deactivate ConfigDAO

ActivityMgr -> ActivityBiz: initActivityRules(typeId)
note right: 初始化活动规则\n设置默认的业务规则
activate ActivityBiz
ActivityBiz -> ActivityDAO: saveActivityRules(rules)
activate ActivityDAO
ActivityDAO -> MongoDB: insert rules
MongoDB --> ActivityDAO: rules saved
deactivate ActivityDAO
ActivityBiz --> ActivityMgr: rules initialized
deactivate ActivityBiz

ActivityMgr -> TriggerSvc: registerTriggers(typeId)
note right: 注册触发器\n设置活动状态变更的自动处理
activate TriggerSvc
TriggerSvc -> ConfigDAO: saveTriggerConfig(triggers)
activate ConfigDAO
ConfigDAO -> MongoDB: insert triggers
MongoDB --> TriggerSvc: triggers saved
deactivate ConfigDAO
TriggerSvc --> ActivityMgr: triggers registered
deactivate TriggerSvc

ActivityMgr -> TxProxy: commitTransaction()
note right: 提交事务\n完成活动类型初始化
TxProxy --> ActivityMgr: commit success
deactivate TxProxy

ActivityMgr --> InitCtrl: initialization result
note left: 返回初始化结果\n包含活动类型ID和状态
deactivate ActivityMgr
InitCtrl --> Admin: 活动类型初始化成功
note left: 返回成功信息\n可以开始使用新活动类型
deactivate InitCtrl

note over Consumer, PaaS
  <b>流程总结：</b>
  1. <b>奖励发放流程</b>：体现了完整的扫码奖励链路，包含用户验证、活动查找、预算检查、奖励计算和发放
  2. <b>预算消费流程</b>：展示了预算管理的核心逻辑，包含权限验证、分布式锁、事务管理和异步处理
  3. <b>活动初始化流程</b>：说明了系统的元数据管理能力，包含PaaS平台集成、配置管理和触发器注册
  
  <b>技术特点：</b>
  • 所有关键操作都使用事务管理确保数据一致性
  • 使用Redis分布式锁防止并发修改冲突
  • 异步处理提高系统性能和用户体验
  • 与外部服务（微信、PaaS平台）的集成
  • 详细的操作日志记录便于审计和问题排查
end note

@enduml
