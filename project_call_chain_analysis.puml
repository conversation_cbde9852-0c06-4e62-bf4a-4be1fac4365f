@startuml CRM-FMCG-TPM项目调用链分析图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle
skinparam packageStyle rectangle

title CRM FMCG TPM 项目调用链分析图

' 定义颜色
skinparam component {
    BackgroundColor<<Controller>> #E1F5FE
    BackgroundColor<<Service>> #F3E5F5
    BackgroundColor<<Manager>> #E8F5E8
    BackgroundColor<<Business>> #FFF3E0
    BackgroundColor<<DAO>> #FFEBEE
    BackgroundColor<<Proxy>> #F1F8E9
    BackgroundColor<<Entity>> #FFF8E1
}

package "Web层 (Controller)" {
    component [TPMActivityObjEnableListController] <<Controller>>
    component [RewardController] <<Controller>>
    component [POCActivityController] <<Controller>>
    component [WithdrawalController] <<Controller>>
    component [MengNiuRewardController] <<Controller>>
    component [InnerDMSController] <<Controller>>
    component [InnerRewardController] <<Controller>>
    component [InnerYLTPMController] <<Controller>>
}

note top of [TPMActivityObjEnableListController]
  TPM活动对象启用列表控制器
  处理活动对象的启用状态管理
end note

note top of [RewardController]
  奖励控制器
  处理奖励规则的增删改查
  扫码奖励等功能
end note

note top of [POCActivityController]
  POC活动控制器
  处理POC(概念验证)活动
  的初始化和管理
end note

package "服务层 (Service)" {
    component [IActivityRewardRuleService] <<Service>>
    component [IPOCActivityService] <<Service>>
    component [IPhysicalRewardService] <<Service>>
    component [WithdrawService] <<Service>>
    component [ScanCodeService] <<Service>>
    component [BudgetNewConsumeRuleService] <<Service>>
    component [BudgetClosureService] <<Service>>
    component [LicenseService] <<Service>>
    component [PhysicalRewardService] <<Service>>
    component [UnlockOuterCodeService] <<Service>>
    component [WechatService] <<Service>>
}

package "管理层 (Manager)" {
    component [IRewardRuleManager] <<Manager>>
    component [ActivityTypeManager] <<Manager>>
    component [BudgetAccrualRuleManager] <<Manager>>
    component [ProofPeriodManager] <<Manager>>
}

package "业务逻辑层 (Business)" {
    component [ActivityService] <<Business>>
    component [StoreBusiness] <<Business>>
    component [TPMTriggerActionService] <<Business>>
    component [IRangeFieldBusiness] <<Business>>
    component [AsyncBudgetDisassemblyService] <<Business>>
    component [TPMDisplayReportService] <<Business>>
    component [IBudgetService] <<Business>>
    component [IBudgetProvisionService] <<Business>>
    component [IRedPacketService] <<Business>>
    component [FmcgSerialNumberService] <<Business>>
}

package "数据访问层 (DAO)" {
    component [BaseDAO] <<DAO>>
    component [UniqueIdBaseDAO] <<DAO>>
    component [ActivityTypeDAO] <<DAO>>
    component [BudgetNewConsumeRuleDAO] <<DAO>>
    component [BudgetAccrualRuleDAO] <<DAO>>
    component [POCRecordDAO] <<DAO>>
    component [ActivityRewardRuleDAO] <<DAO>>
    component [ConfigDAO] <<DAO>>
    component [CommonDAO] <<DAO>>
}

package "实体层 (Entity/PO)" {
    component [MongoPO] <<Entity>>
    component [ActivityTypePO] <<Entity>>
    component [BudgetNewConsumeRulePO] <<Entity>>
    component [POCRecordPO] <<Entity>>
    component [ActivityRewardRulePO] <<Entity>>
    component [ConfigPO] <<Entity>>
    component [BizCodePO] <<Entity>>
}

package "外部服务代理 (Proxy)" {
    component [FmcgServiceProxy] <<Proxy>>
    component [FmcgCrmProxy] <<Proxy>>
    component [CheckinProxy] <<Proxy>>
    component [WechatProxy] <<Proxy>>
    component [PaasDataProxy] <<Proxy>>
    component [CrmWorkflowProxy] <<Proxy>>
    component [TransactionProxy] <<Proxy>>
}

package "契约层 (Contract/API)" {
    component [POCBase] <<Contract>>
    component [GetConsumeObject] <<Contract>>
    component [Freeze/Unfreeze] <<Contract>>
    component [EndConsume] <<Contract>>
    component [GetConfig] <<Contract>>
    component [InnerApiResult] <<Contract>>
}

package "基础设施层" {
    component [MongoDB] as mongo
    component [Redis] as redis
    component [外部API] as external
}

' 调用关系 - Controller到Service
[TPMActivityObjEnableListController] --> [IActivityRewardRuleService]
[RewardController] --> [IActivityRewardRuleService]
[RewardController] --> [IRewardRuleManager]
[RewardController] --> [ScanCodeService]
[RewardController] --> [IPhysicalRewardService]
[POCActivityController] --> [IPOCActivityService]
[WithdrawalController] --> [WithdrawService]
[MengNiuRewardController] --> [ScanCodeService]
[InnerDMSController] --> [IPhysicalRewardService]

' Service到Manager
[IActivityRewardRuleService] --> [IRewardRuleManager]
[BudgetNewConsumeRuleService] --> [BudgetAccrualRuleManager]

' Service到Business
[IPhysicalRewardService] --> [IRedPacketService]
[IPhysicalRewardService] --> [FmcgSerialNumberService]
[BudgetNewConsumeRuleService] --> [IBudgetProvisionService]
[BudgetClosureService] --> [AsyncBudgetDisassemblyService]
[ScanCodeService] --> [ActivityService]
[UnlockOuterCodeService] --> [StoreBusiness]

' Manager到Business
[ActivityTypeManager] --> [ActivityService]
[ActivityTypeManager] --> [TPMTriggerActionService]
[ProofPeriodManager] --> [TPMDisplayReportService]
[BudgetAccrualRuleManager] --> [IBudgetService]

' Business到DAO
[ActivityService] --> [ActivityTypeDAO]
[StoreBusiness] --> [BaseDAO]
[TPMTriggerActionService] --> [UniqueIdBaseDAO]
[IBudgetService] --> [BudgetNewConsumeRuleDAO]
[IBudgetProvisionService] --> [BudgetAccrualRuleDAO]
[TPMDisplayReportService] --> [POCRecordDAO]
[IRedPacketService] --> [ActivityRewardRuleDAO]

' DAO到Entity
[BaseDAO] --> [MongoPO]
[UniqueIdBaseDAO] --> [MongoPO]
[ActivityTypeDAO] --> [ActivityTypePO]
[BudgetNewConsumeRuleDAO] --> [BudgetNewConsumeRulePO]
[POCRecordDAO] --> [POCRecordPO]
[ActivityRewardRuleDAO] --> [ActivityRewardRulePO]
[ConfigDAO] --> [ConfigPO]

' Service到Proxy
[ScanCodeService] --> [FmcgServiceProxy]
[ScanCodeService] --> [CheckinProxy]
[WechatService] --> [WechatProxy]
[LicenseService] --> [PaasDataProxy]
[PhysicalRewardService] --> [CrmWorkflowProxy]
[BudgetNewConsumeRuleService] --> [TransactionProxy]

' DAO到基础设施
[BaseDAO] --> mongo
[UniqueIdBaseDAO] --> mongo
[ActivityTypeDAO] --> mongo
[BudgetNewConsumeRuleDAO] --> mongo

' Service到基础设施
[ScanCodeService] --> redis
[LicenseService] --> redis

' Proxy到外部服务
[FmcgServiceProxy] --> external
[FmcgCrmProxy] --> external
[CheckinProxy] --> external
[WechatProxy] --> external
[PaasDataProxy] --> external
[CrmWorkflowProxy] --> external

' 契约层关系
[TPMActivityObjEnableListController] ..> [POCBase] : uses
[RewardController] ..> [GetConsumeObject] : uses
[InnerDMSController] ..> [InnerApiResult] : uses
[BudgetClosureService] ..> [EndConsume] : uses

note top of [TPMActivityObjEnableListController]
  <b>主要功能:</b>
  • TPM活动对象的启用管理
  • 活动对象列表查询和筛选
  • 活动状态的批量操作
end note

note top of [RewardController]
  <b>主要功能:</b>
  • 奖励规则的CRUD操作
  • 扫码奖励的处理流程
  • 奖励规则的校验和审批
  • 实物奖励的管理
end note

note top of [AsyncBudgetDisassemblyService]
  <b>主要功能:</b>
  • 异步处理预算拆解任务
  • 预算的冻结和解冻操作
  • 分布式锁的管理
  • 预算拆解状态的跟踪
end note

note top of [TPMDisplayReportService]
  <b>主要功能:</b>
  • 陈列数据的收集和处理
  • AI图像识别和分析
  • 陈列报告的生成
  • 陈列数据的校验和审核
end note

note right of [ScanCodeService]
  <b>核心功能:</b>
  • 二维码的扫描和解析
  • 扫码奖励的计算和发放
  • 微信小程序集成
  • 红包和实物奖励的处理
end note

note bottom of mongo
  <b>MongoDB数据库</b>
  • 主要的业务数据存储
  • 活动、预算、奖励等数据
  • 支持复杂查询和聚合操作
end note

note bottom of redis
  <b>Redis缓存</b>
  • 系统缓存和会话管理
  • 分布式锁的实现
  • 异步任务的状态管理
end note

note bottom of external
  <b>外部服务集成</b>
  • 微信小程序和公众号API
  • 支付宝支付和转账服务
  • PaaS平台的元数据服务
  • 第三方AI和数据分析服务
end note

@enduml
